---
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import '../styles/global.css'; // Import global styles

interface Props {
    title: string;
    description?: string; // Optional description meta tag
}

const { title, description = '<PERSON><PERSON><PERSON>, tvorba webových vizitek a IT služby.' } = Astro.props;

// Basic Dark Mode Toggle Logic (persists via localStorage)
// More robust solutions exist, but this is simple for demonstration
const themeScript = `
  const theme = (() => {
    if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
      return localStorage.getItem('theme');
    }
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  })();

  if (theme === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
  window.localStorage.setItem('theme', theme); // Save initial theme

  window.toggleTheme = function() {
    const currentTheme = localStorage.getItem('theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    localStorage.setItem('theme', newTheme);
    document.documentElement.classList.toggle('dark', newTheme === 'dark');
  }
`;
---

<!doctype html>
<html lang="cs" class="scroll-smooth">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <meta name="generator" content={Astro.generator} />
        <meta name="description" content={description} />
        <title>{title}</title>

        {/* Include Google Font (Example: Inter) */}
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">

        {/* Theme toggle script - run early to prevent FOUC */}
        <script is:inline define:vars={{ themeScript }}>
          {Fragment.raw(themeScript)}
        </script>
    </head>
    <body class="bg-[#040A1D] text-stone-400 dark:bg-dark dark:text-light transition-colors duration-300">
        <Header />
        <main>
            <slot /> {/* Page content goes here */}
        </main>
        <Footer />

        
    </body>
</html>