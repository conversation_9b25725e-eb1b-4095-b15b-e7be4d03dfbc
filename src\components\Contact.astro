---
// WARNING: This form needs a backend or service (like Form<PERSON>ree, Netlify Forms) to actually send emails.
const contactEmail = "<EMAIL>"; // Replace with your email
// Basic JS obfuscation for email (not strong security, but deters simple bots)
const emailParts = contactEmail.split('@');
const emailUser = emailParts[0];
const emailDomain = emailParts[1];
---
<section id="contact" class="py-16 md:py-24 bg-light-dark dark:bg-dark-light">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold text-primary dark:text-secondary mb-4">Get In Touch</h2>
      <p class="text-lg text-dark/80 dark:text-light/80 max-w-2xl mx-auto">
        Have a project in mind or just want to say hi? Fill out the form or send me an email.
      </p>
    </div>
    <div class="max-w-xl mx-auto bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg">
      {/* --- FORM --- */}
      {/* IMPORTANT: Set the 'action' attribute to your form endpoint (e.g., Formspree URL) */}
      {/* Example for Formspree: action="https://formspree.io/f/YOUR_FORM_ID" */}
      <form action="#" method="POST" class="space-y-6">
         {/* Add hidden Honeypot field for basic spam protection if your service supports it */}
         <input type="text" name="_gotcha" style="display:none !important">

        <div>
          <label for="name" class="block text-sm font-medium text-dark dark:text-light mb-1">Name</label>
          <input type="text" name="name" id="name" required
                 class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 focus:ring-primary focus:border-primary dark:focus:ring-offset-dark-light dark:focus:border-secondary transition-colors">
        </div>
        <div>
          <label for="email" class="block text-sm font-medium text-dark dark:text-light mb-1">Email</label>
          <input type="email" name="email" id="email" required
                 class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 focus:ring-primary focus:border-primary dark:focus:ring-offset-dark-light dark:focus:border-secondary transition-colors">
        </div>
        <div>
          <label for="message" class="block text-sm font-medium text-dark dark:text-light mb-1">Message</label>
          <textarea name="message" id="message" rows="4" required
                    class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 focus:ring-primary focus:border-primary dark:focus:ring-offset-dark-light dark:focus:border-secondary transition-colors"></textarea>
        </div>
        <div>
          <button type="submit"
                  class="w-full px-6 py-3 bg-primary text-white font-semibold rounded-md shadow hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:focus:ring-offset-dark-light dark:hover:bg-secondary-dark dark:bg-secondary transition-colors duration-300">
            Send Message
          </button>
        </div>
        <p class="text-xs text-center text-gray-500 dark:text-gray-400">
            Note: Form submission requires setup (e.g., Formspree).
        </p>
      </form>
      {/* --- END FORM --- */}

       <div class="text-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <p class="text-dark/80 dark:text-light/80 mb-2">Or reach me directly:</p>
            <a id="contact-email" href="#" class="text-primary dark:text-secondary hover:underline font-medium">
                <!-- Email will be revealed by JS -->
                email [at] domain [dot] com
            </a>
            {/* Simple JS to reveal email on client, slightly obfuscating from basic scrapers */}
            <script define:vars={{ emailUser, emailDomain }}>
                const emailLink = document.getElementById('contact-email');
                if (emailLink) {
                    const mailto = `mailto:${emailUser}@${emailDomain}`;
                    emailLink.href = mailto;
                    emailLink.textContent = `${emailUser}@${emailDomain}`;
                }
            </script>
        </div>
    </div>
  </div>
</section>