@import "tailwindcss";


/* Basic styles */
html {
  scroll-behavior: smooth;
  font-family: 'Inter', sans-serif; /* Match Tailwind config */
}



/* Style for the fixed P5 canvas container */
#p5-canvas-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -10; /* Ensure it's behind everything */
  overflow: hidden; /* Prevent scrollbars if canvas slightly overflows */
}



/* Basic headings styling */
h1, h2, h3 {
  @apply font-bold tracking-tight;
}

h1 { @apply text-4xl md:text-5xl lg:text-6xl; }
h2 { @apply text-3xl md:text-4xl; }
h3 { @apply text-2xl md:text-3xl; }



.custom-m-clr{
  color: #0c0c1e;
}

.text-muted{
  color: #E3E3E3;
}



/* Fade-in animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
@keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }


  .fade-in-title {
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.2s forwards;
  }

  .fade-in-text {
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.6s forwards;
  }

  .fade-in-gradient {
    opacity: 0;
    animation: fadeInScale 1s ease-out 1s forwards;
  }

  .fade-in-left{
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.5s forwards;
  }