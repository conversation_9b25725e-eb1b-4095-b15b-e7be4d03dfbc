---
const navItems = [
  
  { name: 'Tvorba webů', href: '#services' },
  { name: '<PERSON><PERSON><PERSON>', href: '#contact' },
];
---

<header class="sticky top-0 z-40 w-full backdrop-blur bg-[#040A1D] shadow-sm text-stone-200">
  <nav class="container md:w-4xl mx-auto px-4 py-4">
    <div class="flex justify-around items-center">
      <a href="#" class="text-xl font-bold text-stone-200 hover:opacity-80 hover:text-pink-800 ">
        <PERSON><PERSON><PERSON>
      </a>

      <!-- Desktop menu -->
      <div class="hidden md:flex space-x-6 items-center">
        {navItems.map(item => (
          <a href={item.href} class="text-sm font-medium text-stone-200 hover:text-pink-500/70 transition-colors hover:underline">
            {item.name}
          </a>
        ))}
        <!-- <a href="#contact" class="ml-4 px-4 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-700 transition-colors shadow">
          Nezávazně mě kontaktujte
        </a> -->
      </div>

      <!-- Mobile menu button -->
      <button id="mobile-menu-button" class="md:hidden text-stone-300 focus:outline-none transition-transform duration-200">
        <!-- Hamburger icon -->
        <svg id="hamburger-icon" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 6h16M4 12h16m-7 6h7" />
        </svg>
        <!-- Close icon (hidden by default) -->
        <svg id="close-icon" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Mobile menu panel -->
    <div id="mobile-menu" class="hidden md:hidden mt-4 space-y-2">
      {navItems.map(item => (
        <a href={item.href} class="block text-muted px-3 py-2 rounded-md text-sm font-medium fade-in-left bg-pink-800 hover:underline">
          {item.name}
        </a>
      ))}
      <!-- <a href="#contact" class="block px-3 py-2 rounded-md text-sm font-medium bg-blue-800 text-white hover:bg-blue-700">
        Nezávazně mě kontaktujte
      </a> -->
    </div>
  </nav>
</header>

<script>
  const menuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  const hamburgerIcon = document.getElementById('hamburger-icon');
  const closeIcon = document.getElementById('close-icon');

  function toggleMenu() {
    if (mobileMenu && hamburgerIcon && closeIcon) {
      const isHidden = mobileMenu.classList.contains('hidden');

      if (isHidden) {
        // Otevřít menu
        mobileMenu.classList.remove('hidden');
        hamburgerIcon.classList.add('hidden');
        closeIcon.classList.remove('hidden');
      } else {
        // Zavřít menu
        mobileMenu.classList.add('hidden');
        hamburgerIcon.classList.remove('hidden');
        closeIcon.classList.add('hidden');
      }
    }
  }

  if (menuButton) {
    menuButton.addEventListener('click', toggleMenu);
  }

  // Zavřít mobilní menu při kliknutí na odkaz
  if (mobileMenu) {
    const menuLinks = mobileMenu.querySelectorAll('a');
    menuLinks.forEach(link => {
      link.addEventListener('click', () => {
        mobileMenu.classList.add('hidden');
        if (hamburgerIcon && closeIcon) {
          hamburgerIcon.classList.remove('hidden');
          closeIcon.classList.add('hidden');
        }
      });
    });
  }
</script>
