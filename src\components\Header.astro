---
const navItems = [
  
  { name: 'T<PERSON><PERSON> webů', href: '#services' },
  { name: '<PERSON><PERSON><PERSON>', href: '#contact' },
];
---

<header class="sticky top-0 z-40 w-full backdrop-blur bg-[#040A1D] shadow-sm text-stone-200">
  <nav class="container mx-auto px-4 py-4">
    <div class="flex justify-between items-center">
      <a href="#hero" class="text-xl font-bold text-stone-200 hover:opacity-80 hover:text-pink-500/70 ">
        <PERSON><PERSON><PERSON>
      </a>

      <!-- Desktop menu -->
      <div class="hidden md:flex space-x-6 items-center">
        {navItems.map(item => (
          <a href={item.href} class="text-sm font-medium text-stone-200 hover:text-pink-500/70 transition-colors hover:underline">
            {item.name}
          </a>
        ))}
        <!-- <a href="#contact" class="ml-4 px-4 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-700 transition-colors shadow">
          Nezávazně mě kontaktujte
        </a> -->
      </div>

      <!-- Mobile menu button -->
      <button id="mobile-menu-button" class="md:hidden text-gray-800 focus:outline-none">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 6h16M4 12h16m-7 6h7" />
        </svg>
      </button>
    </div>

    <!-- Mobile menu panel -->
    <div id="mobile-menu" class="hidden md:hidden mt-4 space-y-2">
      {navItems.map(item => (
        <a href={item.href} class="block text-black px-3 py-2 rounded-md text-sm font-medium hover:underline">
          {item.name}
        </a>
      ))}
      <!-- <a href="#contact" class="block px-3 py-2 rounded-md text-sm font-medium bg-blue-800 text-white hover:bg-blue-700">
        Nezávazně mě kontaktujte
      </a> -->
    </div>
  </nav>
</header>

<script>
  const menuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');

  menuButton.addEventListener('click', () => {
    mobileMenu.classList.toggle('hidden');
  });

  // Zavřít mobilní menu při kliknutí na odkaz
  const menuLinks = mobileMenu.querySelectorAll('a');
  menuLinks.forEach(link => {
    link.addEventListener('click', () => {
      mobileMenu.classList.add('hidden');
    });
  });
</script>
