---
// Placeholder Data
const posts = [
    { title: "Why <PERSON><PERSON> is Great for Portfolios", date: "Oct 26, 2023", excerpt: "A quick look at <PERSON><PERSON>'s benefits...", link: "#" },
    { title: "P5.js Fun: Creative Backgrounds", date: "Oct 20, 2023", excerpt: "Exploring generative art for the web...", link: "#" },
];
---
<section id="blog" class="py-16 md:py-24 bg-light dark:bg-dark">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-primary dark:text-secondary mb-4">From the Blog</h2>
            <p class="text-lg text-dark/80 dark:text-light/80 max-w-2xl mx-auto">
                Latest thoughts, tutorials, and insights. (Or link to your external blog!)
            </p>
        </div>
         {/* Check if posts exist before mapping */}
        {posts && posts.length > 0 ? (
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                {posts.map(post => (
                    <a href={post.link} class="block bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 group">
                        <h3 class="text-xl font-semibold mb-2 text-dark dark:text-light group-hover:text-primary dark:group-hover:text-secondary transition-colors">{post.title}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">{post.date}</p>
                        <p class="text-dark/70 dark:text-light/70 mb-4">{post.excerpt}</p>
                        <span class="text-primary dark:text-secondary font-medium group-hover:underline">Read More →</span>
                    </a>
                ))}
            </div>
             <div class="text-center mt-12">
                 <a href="#" class="inline-block px-6 py-3 border border-primary dark:border-secondary text-primary dark:text-secondary font-semibold rounded-lg hover:bg-primary/10 dark:hover:bg-secondary/10 transition-colors duration-300">
                     View All Posts (Coming Soon / Link)
                 </a>
             </div>
        ) : (
            <p class="text-center text-dark/70 dark:text-light/70">Blog posts coming soon!</p>
        )}
    </div>
</section>