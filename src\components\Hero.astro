---
import P5Sketch from './P5Sketch.astro';
---
<section id="hero" class="relative min-h-screen flex items-center justify-center text-center overflow-hidden">
  
  <P5Sketch />

  
  <div class="relative z-10 p-8 max-w-5xl w-full">
     <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold mb-4 md:leading-16 fade-in-title">

        <span class="block text-stone-200 mt-2 tracking-tighter fade-in-text">Tvořím webové stránky pomocí <span class="w-fit bg-gradient-to-r from-fuchsia-600 to-pink-600 bg-clip-text text-transparent fade-in-gradient">ASTROJS </span></span>
    </h1>
    
    <p class="text-lg md:text-xl lg:text-2xl text-muted mb-8 max-w-xl mx-auto leading-relaxed fade-in-text">
      Specializuji se na tvorbu webů pomocí AstroJS - jedná se o moderní framework pro vytváření statických webů s vysokou rychlostí
    </p>
    
  </div>

   

</section>

<style>
  /* Add text shadow for better readability over the potentially busy background */
  .text-shadow-lg {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  }
  .text-shadow {
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
  }

  
</style>