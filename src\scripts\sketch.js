let particles = [];
const numParticles = 50; // Adjust density
const trailLength = 8; // Number of trail points to keep

const sketch = (p) => {
  p.setup = () => {
    // Create canvas matching window size and parent it to the container
    let canvas = p.createCanvas(p.windowWidth, p.windowHeight);
    canvas.parent('p5-canvas-container'); // Attach to the div created in P5Sketch.astro
    p.noStroke();

    // Initialize particles
    for (let i = 0; i < numParticles; i++) {
      particles.push({
        x: p.random(p.width),
        y: p.random(p.height),
        vx: p.random(-0.5, 0.5), // Slower horizontal velocity
        vy: p.random(-1, -0.5),   // Move mostly upwards
        alpha: p.random(50, 150),
        size: p.random(2, 5),
        color: p.color(236, 72, 153, p.random(50, 100)), // Pink color (similar to fuchsia-500/pink-500)
        // RGB values: 236, 72, 153 creates a nice pink color
        trail: [] // Array to store previous positions for trail effect
      });
    }
  };

  p.draw = () => {
    // Clear background completely - no transparency needed for trails
    p.clear();

    particles.forEach(particle => {
      // Store current position in trail before updating
      particle.trail.push({ x: particle.x, y: particle.y });

      // Keep trail length limited
      if (particle.trail.length > trailLength) {
        particle.trail.shift(); // Remove oldest position
      }

      // Update position
      particle.x += particle.vx;
      particle.y += particle.vy;

      // Wrap around edges
      if (particle.x < 0) particle.x = p.width;
      if (particle.x > p.width) particle.x = 0;
      if (particle.y < 0) particle.y = p.height;
      // Don't wrap bottom to top, let them float up and reappear

      // Draw trail with fading alpha
      for (let i = 0; i < particle.trail.length; i++) {
        const trailPoint = particle.trail[i];
        const trailAlpha = p.map(i, 0, particle.trail.length - 1, 10, particle.alpha);
        const trailSize = p.map(i, 0, particle.trail.length - 1, particle.size * 0.3, particle.size);

        p.fill(236, 72, 153, trailAlpha); // Pink with fading alpha
        p.ellipse(trailPoint.x, trailPoint.y, trailSize, trailSize);
      }

      // Draw main particle
      p.fill(236, 72, 153, particle.alpha);
      p.ellipse(particle.x, particle.y, particle.size, particle.size);
    });
  };

  p.windowResized = () => {
    p.resizeCanvas(p.windowWidth, p.windowHeight);
     // Optional: Reinitialize particles if needed on resize
     // particles = []; // Reset
     // for (let i = 0; i < numParticles; i++) { ... } // Re-populate
  };
};

export default sketch; // Export the sketch function