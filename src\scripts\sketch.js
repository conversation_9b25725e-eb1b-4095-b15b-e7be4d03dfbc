let particles = [];
const numParticles = 50; // Adjust density

const sketch = (p) => {
  p.setup = () => {
    // Create canvas matching window size and parent it to the container
    let canvas = p.createCanvas(p.windowWidth, p.windowHeight);
    canvas.parent('p5-canvas-container'); // Attach to the div created in P5Sketch.astro
    p.noStroke();

    // Initialize particles
    for (let i = 0; i < numParticles; i++) {
      particles.push({
        x: p.random(p.width),
        y: p.random(p.height),
        vx: p.random(-0.5, 0.5), // Slower horizontal velocity
        vy: p.random(-1, -0.5),   // Move mostly upwards
        alpha: p.random(50, 150),
        size: p.random(2, 5),
        color: p.color(236, 72, 153, p.random(50, 100)) // Pink color (similar to fuchsia-500/pink-500)
        // RGB values: 236, 72, 153 creates a nice pink color
      });
    }
  };

  p.draw = () => {
    // Subtle background - can match light/dark theme potentially
    // Use a slightly transparent background to create trails effect
    p.background(
      document.documentElement.classList.contains('dark') ? 'rgba(17, 24, 39, 0.1)' : 'rgba(249, 250, 251, 0.1)'
      // 17, 24, 39 is dark.DEFAULT, 249, 250, 251 is light.DEFAULT
    );


    particles.forEach(particle => {
      // Update position
      particle.x += particle.vx;
      particle.y += particle.vy;

      // Wrap around edges
      if (particle.x < 0) particle.x = p.width;
      if (particle.x > p.width) particle.x = 0;
      if (particle.y < 0) particle.y = p.height;
      // Don't wrap bottom to top, let them float up and reappear

      // Draw particle
      // Use the pre-defined color object
      const particleColor = particle.color;
      particleColor.setAlpha(particle.alpha); // Update alpha if needed dynamically
      p.fill(particleColor);
      p.ellipse(particle.x, particle.y, particle.size, particle.size);
    });
  };

  p.windowResized = () => {
    p.resizeCanvas(p.windowWidth, p.windowHeight);
     // Optional: Reinitialize particles if needed on resize
     // particles = []; // Reset
     // for (let i = 0; i < numParticles; i++) { ... } // Re-populate
  };
};

export default sketch; // Export the sketch function