---
const services = [
  { title: "Web Development", description: "Building fast, responsive, and modern websites using technologies like Astro, React, Vue, etc.", icon: "code" },
  { title: "UI/UX Design", description: "Creating intuitive and beautiful user interfaces focused on user experience.", icon: "design" },
  { title: "Consulting", description: "Providing expert advice to help you make the best technical and design decisions.", icon: "consult" },
   { title: "SEO Optimization", description: "Improving your website's visibility on search engines.", icon: "seo" },
];

// Placeholder simple icons (Replace with actual SVG icons later)
const icons = {
  code: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-primary dark:text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" /></svg>`,
  design: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-primary dark:text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>`,
  consult: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-primary dark:text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" /></svg>`,
  seo: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-primary dark:text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>`
}
---
<section id="services" class="py-16 md:py-24 bg-light-dark dark:bg-dark-light">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold text-primary dark:text-secondary mb-4">My Services</h2>
      <p class="text-lg text-dark/80 dark:text-light/80 max-w-2xl mx-auto">
        Offering a range of services to help you succeed online.
      </p>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      {services.map(service => (
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 text-center">
          <div class="mb-4 inline-block p-3 rounded-full bg-primary/10 dark:bg-secondary/10">
             <Fragment set:html={icons[service.icon]} />
          </div>
          <h3 class="text-xl font-semibold mb-3 text-dark dark:text-light">{service.title}</h3>
          <p class="text-dark/70 dark:text-light/70">{service.description}</p>
        </div>
      ))}
    </div>
  </div>
</section>