---
// No props needed for this simple version
---
<div id="p5-canvas-container">
    {/* This div will hold the canvas generated by p5.js */}
  </div>
  
  <script>
    // Import p5 dynamically only on the client-side
    import('p5').then(p5Module => {
      const p5 = p5Module.default;
      // Import our sketch logic
      import('../scripts/sketch.js').then(sketchModule => {
        const sketch = sketchModule.default;
        // Instantiate p5, passing the sketch function
        // p5 will automatically use the 'p5-canvas-container' div via sketch.js setup
        new p5(sketch);
      });
    }).catch(error => {
      console.error("Failed to load p5.js or sketch:", error);
    });
  </script>